package rabbitmq

import (
	"context"
	"fmt"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	log "github.com/sirupsen/logrus"
)

// Pool 全局连接池
var Pool *ConnectionPool

// RabbitMQ 结构体封装了 RabbitMQ 的操作
type RabbitMQ struct {
	// 队列名称
	QueueName string
	// 交换机
	Exchange string
	// 路由key
	Key string
	// 死信交换机
	DeadLetterExchange string
	// 死信路由key
	DeadLetterRoutingKey string
}

// InitRabbitMQ 初始化 RabbitMQ 连接池
func InitRabbitMQ() {
	// 创建连接池配置
	poolConfig := &PoolConfig{
		MaxConnections:  10,               // 最大连接数
		IdleTimeout:     30 * time.Second, // 空闲连接超时时间
		MaxIdleTime:     5 * time.Minute,  // 最大空闲时间
		ConnTimeout:     5 * time.Second,  // 连接超时时间
		CleanupInterval: 1 * time.Minute,  // 清理间隔
	}

	// 创建连接池
	Pool = NewConnectionPool(poolConfig)

	// 测试连接
	conn, err := Pool.Get(context.Background())
	if err != nil {
		log.Errorf("RabbitMQ 连接池初始化失败: %v", err)
		return
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	log.Info("RabbitMQ 连接池初始化成功")
}

// NewRabbitMQ 创建一个新的 RabbitMQ 实例
func NewRabbitMQ(queueName, exchange, key string) *RabbitMQ {
	return &RabbitMQ{
		QueueName:            queueName,
		Exchange:             exchange,
		Key:                  key,
		DeadLetterExchange:   exchange + ".dead", // 默认死信交换机名称
		DeadLetterRoutingKey: key + ".dead",      // 默认死信路由key
	}
}

// declareDeadLetterQueue 声明死信队列和交换机
func (r *RabbitMQ) declareDeadLetterQueue(conn *ChannelConn) error {
	// 声明死信交换机
	err := conn.Channel.ExchangeDeclare(
		r.DeadLetterExchange, // 死信交换机名称
		"direct",             // 交换机类型
		true,                 // 是否持久化
		false,                // 是否自动删除
		false,                // 是否内置
		false,                // 是否阻塞
		nil,                  // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明死信交换机失败: %w", err)
	}

	// 声明死信队列
	deadQueueName := r.QueueName + ".dead"
	_, err = conn.Channel.QueueDeclare(
		deadQueueName, // 死信队列名称
		true,          // 是否持久化
		false,         // 是否自动删除
		false,         // 是否具有排他性
		false,         // 是否阻塞
		nil,           // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明死信队列失败: %w", err)
	}

	// 绑定死信队列到死信交换机
	err = conn.Channel.QueueBind(
		deadQueueName,          // 死信队列名称
		r.DeadLetterRoutingKey, // 死信路由键
		r.DeadLetterExchange,   // 死信交换机名称
		false,                  // 是否等待
		nil,                    // 额外参数
	)
	if err != nil {
		return fmt.Errorf("绑定死信队列失败: %w", err)
	}

	return nil
}

// declareQueueWithDeadLetter 声明带死信配置的队列
func (r *RabbitMQ) declareQueueWithDeadLetter(conn *ChannelConn, durable, autoDelete, exclusive bool) (amqp.Queue, error) {
	// 先声明死信队列
	if err := r.declareDeadLetterQueue(conn); err != nil {
		return amqp.Queue{}, err
	}

	// 配置队列参数，包含死信配置
	args := amqp.Table{
		"x-dead-letter-exchange":    r.DeadLetterExchange,   // 死信交换机
		"x-dead-letter-routing-key": r.DeadLetterRoutingKey, // 死信路由键
	}

	// 声明主队列
	queue, err := conn.Channel.QueueDeclare(
		r.QueueName, // 队列名称
		durable,     // 是否持久化
		autoDelete,  // 是否自动删除
		exclusive,   // 是否具有排他性
		false,       // 是否阻塞
		args,        // 队列参数，包含死信配置
	)
	if err != nil {
		return amqp.Queue{}, fmt.Errorf("声明队列失败: %w", err)
	}

	return queue, nil
}

// PublishSimple 简单模式下发布消息（带死信队列）
func (r *RabbitMQ) PublishSimple(message string) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明带死信配置的队列
	_, err = r.declareQueueWithDeadLetter(conn, true, false, false)
	if err != nil {
		return err
	}

	// 发送消息
	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = conn.Channel.PublishWithContext(
		ctx,
		"",          // 交换机
		r.QueueName, // 路由key
		false,       // 是否强制
		false,       // 是否立即
		amqp.Publishing{
			ContentType: "application/json",
			Body:        []byte(message),
		},
	)
	if err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	return nil
}

// PublishDirect 直连模式下发布消息
func (r *RabbitMQ) PublishDirect(message string, durable, autoDelete, exclusive bool) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明交换机
	err = conn.Channel.ExchangeDeclare(
		r.Exchange, // 交换机名称
		"direct",   // 交换机类型
		true,       // 是否持久化
		false,      // 是否自动删除
		false,      // 是否内置
		false,      // 是否阻塞
		nil,        // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明队列（如果队列名不为空）
	if r.QueueName != "" {
		// 声明带死信配置的队列
		queue, err := r.declareQueueWithDeadLetter(conn, durable, autoDelete, exclusive)
		if err != nil {
			return err
		}

		// 绑定队列到交换机
		err = conn.Channel.QueueBind(
			queue.Name, // 队列名称
			r.Key,      // 路由键
			r.Exchange, // 交换机名称
			false,      // 是否等待
			nil,        // 额外参数
		)
		if err != nil {
			return fmt.Errorf("绑定队列失败: %w", err)
		}
	}

	// 发送消息
	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = conn.Channel.PublishWithContext(
		ctx,
		r.Exchange, // 交换机
		r.Key,      // 路由key
		false,      // 是否强制
		false,      // 是否立即
		amqp.Publishing{
			ContentType: "application/json",
			Body:        []byte(message),
		},
	)
	if err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	return nil
}

// PublishTopic 主题模式下发布消息
func (r *RabbitMQ) PublishTopic(message string, durable, autoDelete, exclusive bool) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明交换机
	err = conn.Channel.ExchangeDeclare(
		r.Exchange, // 交换机名称
		"topic",    // 交换机类型
		true,       // 是否持久化
		false,      // 是否自动删除
		false,      // 是否内置
		false,      // 是否阻塞
		nil,        // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明队列（如果队列名不为空）
	if r.QueueName != "" {
		// 声明带死信配置的队列
		queue, err := r.declareQueueWithDeadLetter(conn, durable, autoDelete, exclusive)
		if err != nil {
			return err
		}

		// 绑定队列到交换机
		err = conn.Channel.QueueBind(
			queue.Name, // 队列名称
			r.Key,      // 路由键
			r.Exchange, // 交换机名称
			false,      // 是否等待
			nil,        // 额外参数
		)
		if err != nil {
			return fmt.Errorf("绑定队列失败: %w", err)
		}
	}

	// 发送消息
	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = conn.Channel.PublishWithContext(
		ctx,
		r.Exchange, // 交换机
		r.Key,      // 路由key
		false,      // 是否强制
		false,      // 是否立即
		amqp.Publishing{
			ContentType: "application/json",
			Body:        []byte(message),
		},
	)
	if err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	return nil
}

// PublishFanout 发布订阅模式下发布消息
func (r *RabbitMQ) PublishFanout(message string, durable, autoDelete, exclusive bool) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明交换机
	err = conn.Channel.ExchangeDeclare(
		r.Exchange, // 交换机名称
		"fanout",   // 交换机类型
		true,       // 是否持久化
		false,      // 是否自动删除
		false,      // 是否内置
		false,      // 是否阻塞
		nil,        // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明队列（如果队列名不为空）
	if r.QueueName != "" {
		// 声明带死信配置的队列
		queue, err := r.declareQueueWithDeadLetter(conn, durable, autoDelete, exclusive)
		if err != nil {
			return err
		}

		// 绑定队列到交换机（fanout模式不需要路由键）
		err = conn.Channel.QueueBind(
			queue.Name, // 队列名称
			"",         // 路由键为空
			r.Exchange, // 交换机名称
			false,      // 是否等待
			nil,        // 额外参数
		)
		if err != nil {
			return fmt.Errorf("绑定队列失败: %w", err)
		}
	}

	// 发送消息
	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = conn.Channel.PublishWithContext(
		ctx,
		r.Exchange, // 交换机
		"",         // 路由key在发布订阅模式下为空
		false,      // 是否强制
		false,      // 是否立即
		amqp.Publishing{
			ContentType: "application/json",
			Body:        []byte(message),
		},
	)
	if err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	return nil
}

// ConsumeSimple 简单模式下消费消息
func (r *RabbitMQ) ConsumeSimple(handler func([]byte) (bool, error)) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明带死信配置的队列
	_, err = r.declareQueueWithDeadLetter(conn, true, false, false)
	if err != nil {
		return err
	}

	// 开始消费消息
	msgs, err := conn.Channel.Consume(
		r.QueueName, // 队列名称
		"",          // 消费者标签
		false,       // 是否自动确认
		false,       // 是否独占
		false,       // 是否阻塞
		false,       // 是否等待
		nil,         // 额外参数
	)
	if err != nil {
		return fmt.Errorf("开始消费消息失败: %w", err)
	}

	// 处理消息
	for msg := range msgs {
		// 调用处理函数
		if retry, err := handler(msg.Body); err != nil {
			log.Errorf("处理消息失败: %v", err)
			// 拒绝消息，不重新入队
			if retry {
				_ = msg.Nack(false, true)
			} else {
				_ = msg.Nack(false, false)
			}
		} else {
			// 确认消息
			_ = msg.Ack(false)
		}
	}

	return nil
}

// ConsumeDirect 直连模式下消费消息
func (r *RabbitMQ) ConsumeDirect(handler func([]byte) error) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明交换机
	err = conn.Channel.ExchangeDeclare(
		r.Exchange, // 交换机名称
		"direct",   // 交换机类型
		true,       // 是否持久化
		false,      // 是否自动删除
		false,      // 是否内置
		false,      // 是否阻塞
		nil,        // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明带死信配置的队列
	queue, err := r.declareQueueWithDeadLetter(conn, true, false, false)
	if err != nil {
		return err
	}

	// 绑定队列到交换机
	err = conn.Channel.QueueBind(
		queue.Name, // 队列名称
		r.Key,      // 路由键
		r.Exchange, // 交换机名称
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("绑定队列失败: %w", err)
	}

	// 开始消费消息
	msgs, err := conn.Channel.Consume(
		queue.Name, // 队列名称
		"",         // 消费者标签
		false,      // 是否自动确认
		false,      // 是否独占
		false,      // 是否阻塞
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("开始消费消息失败: %w", err)
	}

	// 处理消息
	for msg := range msgs {
		// 调用处理函数
		if err := handler(msg.Body); err != nil {
			log.Errorf("处理消息失败: %v", err)
			// 拒绝消息，不重新入队
			_ = msg.Nack(false, false)
		} else {
			// 确认消息
			_ = msg.Ack(false)
		}
	}

	return nil
}

// ConsumeTopic 主题模式下消费消息
func (r *RabbitMQ) ConsumeTopic(handler func([]byte) error) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明交换机
	err = conn.Channel.ExchangeDeclare(
		r.Exchange, // 交换机名称
		"topic",    // 交换机类型
		true,       // 是否持久化
		false,      // 是否自动删除
		false,      // 是否内置
		false,      // 是否阻塞
		nil,        // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明带死信配置的队列
	queue, err := r.declareQueueWithDeadLetter(conn, true, false, false)
	if err != nil {
		return err
	}

	// 绑定队列到交换机
	err = conn.Channel.QueueBind(
		queue.Name, // 队列名称
		r.Key,      // 路由键
		r.Exchange, // 交换机名称
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("绑定队列失败: %w", err)
	}

	// 开始消费消息
	msgs, err := conn.Channel.Consume(
		queue.Name, // 队列名称
		"",         // 消费者标签
		false,      // 是否自动确认
		false,      // 是否独占
		false,      // 是否阻塞
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("开始消费消息失败: %w", err)
	}

	// 处理消息
	for msg := range msgs {
		// 调用处理函数
		if err := handler(msg.Body); err != nil {
			log.Errorf("处理消息失败: %v", err)
			// 拒绝消息，不重新入队
			_ = msg.Nack(false, false)
		} else {
			// 确认消息
			_ = msg.Ack(false)
		}
	}

	return nil
}

// ConsumeFanout 发布订阅模式下消费消息
func (r *RabbitMQ) ConsumeFanout(handler func([]byte) error) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明交换机
	err = conn.Channel.ExchangeDeclare(
		r.Exchange, // 交换机名称
		"fanout",   // 交换机类型
		true,       // 是否持久化
		false,      // 是否自动删除
		false,      // 是否内置
		false,      // 是否阻塞
		nil,        // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明队列（如果队列名为空，则创建临时队列）
	var queue amqp.Queue
	if r.QueueName == "" {
		// 创建临时队列（临时队列不需要死信配置）
		queue, err = conn.Channel.QueueDeclare(
			"",    // 队列名称为空，系统自动生成
			false, // 不持久化
			true,  // 自动删除
			true,  // 独占
			false, // 不等待
			nil,   // 额外属性
		)
		if err != nil {
			return fmt.Errorf("声明临时队列失败: %w", err)
		}
	} else {
		// 使用指定队列名，声明带死信配置的队列
		queue, err = r.declareQueueWithDeadLetter(conn, true, false, false)
		if err != nil {
			return err
		}
	}

	// 绑定队列到交换机（fanout模式不需要路由键）
	err = conn.Channel.QueueBind(
		queue.Name, // 队列名称
		"",         // 路由键为空
		r.Exchange, // 交换机名称
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("绑定队列失败: %w", err)
	}

	// 开始消费消息
	msgs, err := conn.Channel.Consume(
		queue.Name, // 队列名称
		"",         // 消费者标签
		false,      // 是否自动确认
		false,      // 是否独占
		false,      // 是否阻塞
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("开始消费消息失败: %w", err)
	}

	// 处理消息
	for msg := range msgs {
		// 调用处理函数
		if err := handler(msg.Body); err != nil {
			log.Errorf("处理消息失败: %v", err)
			// 拒绝消息，不重新入队
			_ = msg.Nack(false, false)
		} else {
			// 确认消息
			_ = msg.Ack(false)
		}
	}

	return nil
}

// ConsumeDeadLetter 消费死信队列消息
func (r *RabbitMQ) ConsumeDeadLetter(handler func([]byte) error) error {
	// 从连接池获取连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := Pool.Get(ctx)
	if err != nil {
		return fmt.Errorf("获取RabbitMQ连接失败: %w", err)
	}
	defer func() {
		_ = Pool.Put(conn)
	}()

	// 声明死信交换机
	err = conn.Channel.ExchangeDeclare(
		r.DeadLetterExchange, // 死信交换机名称
		"direct",             // 交换机类型
		true,                 // 是否持久化
		false,                // 是否自动删除
		false,                // 是否内置
		false,                // 是否阻塞
		nil,                  // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明死信交换机失败: %w", err)
	}

	// 声明死信队列
	deadQueueName := r.QueueName + ".dead"
	queue, err := conn.Channel.QueueDeclare(
		deadQueueName, // 死信队列名称
		true,          // 是否持久化
		false,         // 是否自动删除
		false,         // 是否具有排他性
		false,         // 是否阻塞
		nil,           // 额外属性
	)
	if err != nil {
		return fmt.Errorf("声明死信队列失败: %w", err)
	}

	// 绑定死信队列到死信交换机
	err = conn.Channel.QueueBind(
		queue.Name,             // 死信队列名称
		r.DeadLetterRoutingKey, // 死信路由键
		r.DeadLetterExchange,   // 死信交换机名称
		false,                  // 是否等待
		nil,                    // 额外参数
	)
	if err != nil {
		return fmt.Errorf("绑定死信队列失败: %w", err)
	}

	// 开始消费死信消息
	msgs, err := conn.Channel.Consume(
		queue.Name, // 死信队列名称
		"",         // 消费者标签
		false,      // 是否自动确认
		false,      // 是否独占
		false,      // 是否阻塞
		false,      // 是否等待
		nil,        // 额外参数
	)
	if err != nil {
		return fmt.Errorf("开始消费死信消息失败: %w", err)
	}

	// 处理死信消息
	for msg := range msgs {
		// 调用处理函数
		if err := handler(msg.Body); err != nil {
			log.Errorf("处理死信消息失败: %v", err)
			// 拒绝消息，不重新入队（避免死信消息再次进入死信队列）
			_ = msg.Nack(false, false)
		} else {
			log.Infof("死信消息处理成功")
			// 确认消息
			_ = msg.Ack(false)
		}
	}

	return nil
}

// GetDeadLetterQueueName 获取死信队列名称
func (r *RabbitMQ) GetDeadLetterQueueName() string {
	return r.QueueName + ".dead"
}

// GetDeadLetterExchangeName 获取死信交换机名称
func (r *RabbitMQ) GetDeadLetterExchangeName() string {
	return r.DeadLetterExchange
}

// GetDeadLetterRoutingKey 获取死信路由键
func (r *RabbitMQ) GetDeadLetterRoutingKey() string {
	return r.DeadLetterRoutingKey
}
